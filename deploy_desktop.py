#!/usr/bin/env python3
"""
Desktop Deployment Script for YOLO License Plate Detection
Creates a standalone executable for easy distribution
"""

import os
import sys
import subprocess
import shutil

def create_desktop_deployment():
    """Create a desktop deployment package"""
    
    print("🚀 Creating Desktop Deployment Package...")
    
    # Create deployment directory
    deploy_dir = "license_plate_detector_app"
    if os.path.exists(deploy_dir):
        shutil.rmtree(deploy_dir)
    os.makedirs(deploy_dir)
    
    # Copy necessary files
    files_to_copy = [
        ("../src/object_detection_yolo.py", "license_plate_detector.py"),
        ("../model/", "model/"),
        ("../requirements.txt", "requirements.txt"),
        ("../README.md", "README.md"),
    ]
    
    for src, dst in files_to_copy:
        src_path = src
        dst_path = os.path.join(deploy_dir, dst)
        
        if os.path.isdir(src_path):
            shutil.copytree(src_path, dst_path)
        else:
            os.makedirs(os.path.dirname(dst_path), exist_ok=True)
            shutil.copy2(src_path, dst_path)
    
    # Create launcher script
    launcher_content = '''@echo off
echo Starting YOLO License Plate Detection System...
echo.
echo Choose an option:
echo 1. Real-time webcam detection
echo 2. Process single image
echo 3. Process video file
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    python license_plate_detector.py
) else if "%choice%"=="2" (
    set /p image_path="Enter image path: "
    python license_plate_detector.py --image "%image_path%"
) else if "%choice%"=="3" (
    set /p video_path="Enter video path: "
    python license_plate_detector.py --video "%video_path%"
) else (
    echo Invalid choice!
    pause
)

echo.
echo Detection completed! Check the CSV file for results.
pause
'''
    
    with open(os.path.join(deploy_dir, "run_detector.bat"), "w") as f:
        f.write(launcher_content)
    
    # Create installation script
    install_content = '''@echo off
echo Installing YOLO License Plate Detection System...
echo.
echo Installing Python dependencies...
pip install -r requirements.txt
echo.
echo Installation completed!
echo.
echo To run the system, double-click "run_detector.bat"
pause
'''
    
    with open(os.path.join(deploy_dir, "install.bat"), "w") as f:
        f.write(install_content)
    
    # Create README for deployment
    readme_content = '''# YOLO License Plate Detection System - Desktop App

## Installation
1. Run `install.bat` to install dependencies
2. Double-click `run_detector.bat` to start the application

## Features
- Real-time webcam license plate detection
- Image and video processing
- Automatic OCR text extraction
- CSV logging of all detections
- OpenCV GUI display

## Usage
- Choose option 1 for real-time webcam detection
- Choose option 2 to process a single image
- Choose option 3 to process a video file

## Output
- Detected license plates saved as individual images
- All detections logged to `license_plate_detections.csv`
- Processed images/videos saved with detection boxes

## Controls
- Press 'q' or ESC to quit the GUI
- All results are automatically saved
'''
    
    with open(os.path.join(deploy_dir, "DEPLOYMENT_README.md"), "w") as f:
        f.write(readme_content)
    
    print(f"✅ Desktop deployment package created in: {deploy_dir}/")
    print("📦 Package contents:")
    for root, dirs, files in os.walk(deploy_dir):
        level = root.replace(deploy_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")

def create_executable():
    """Create standalone executable using PyInstaller"""
    try:
        print("🔧 Creating standalone executable...")
        
        # Install PyInstaller if not available
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        
        # Create executable
        cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--name", "LicensePlateDetector",
            "--add-data", "model;model",
            "--hidden-import", "cv2",
            "--hidden-import", "pytesseract",
            "src/object_detection_yolo.py"
        ]
        
        subprocess.run(cmd, check=True)
        print("✅ Executable created in dist/ folder")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error creating executable: {e}")
        print("💡 You can still use the desktop deployment package")

if __name__ == "__main__":
    create_desktop_deployment()
    
    # Ask if user wants to create executable
    create_exe = input("\n🤔 Do you want to create a standalone executable? (y/n): ")
    if create_exe.lower() == 'y':
        create_executable()
    
    print("\n🎉 Deployment completed!")
    print("📁 Your application is ready for distribution!")
