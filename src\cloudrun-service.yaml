apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: license-plate-detector
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
    spec:
      containerConcurrency: 10
      containers:
      - image: gcr.io/PROJECT_ID/license-plate-detector
        ports:
        - containerPort: 5000
        env:
        - name: PYTHONPATH
          value: "/app"
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
