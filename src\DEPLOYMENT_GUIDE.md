# 🚀 YOLO License Plate Detection - Deployment Guide

## Deployment Options

### 1. 🖥️ Desktop Application
```bash
python deploy_desktop.py
```
- Creates standalone desktop application
- Includes batch files for easy launching
- No internet connection required after installation

### 2. 🌐 Web Application (Local)
```bash
pip install flask
python web_app.py
```
- Access at http://localhost:5000
- Web interface for image upload and webcam detection
- Real-time results display

### 3. 🐳 Docker Deployment
```bash
# Build and run locally
docker build -t license-plate-detector .
docker run -p 5000:5000 license-plate-detector

# Or use Docker Compose
docker-compose up
```

### 4. ☁️ Cloud Deployment

#### AWS Elastic Beanstalk
```bash
# Install EB CLI
pip install awsebcli

# Deploy
./deploy_aws.sh
```

#### Google Cloud Platform
```bash
# Install gcloud CLI
# Set project ID in deploy_gcp.sh
./deploy_gcp.sh
```

#### Microsoft Azure
```bash
# Install Azure CLI
# Update registry in azure-container-template.json
./deploy_azure.sh
```

## 📋 Prerequisites

### System Requirements
- Python 3.8+
- OpenCV with GUI support
- Tesseract OCR
- 4GB+ RAM recommended
- GPU optional (for faster processing)

### Dependencies
```bash
pip install -r requirements.txt
```

## 🔧 Configuration

### Environment Variables
- `TESSDATA_PREFIX`: Path to Tesseract data files
- `PYTHONPATH`: Application path

### Model Files
Ensure these files are present:
- `model/weights/model.weights`
- `model/config/darknet-yolov3.cfg`
- `model/classes.names`

## 🚀 Quick Start

1. **Desktop**: Run `python deploy_desktop.py`
2. **Web**: Run `python web_app.py`
3. **Docker**: Run `docker-compose up`
4. **Cloud**: Choose your platform and run the deployment script

## 📊 Monitoring & Logs

- Detection results: `license_plate_detections.csv`
- Application logs: Check console output
- Error handling: Built-in exception handling

## 🔒 Security Considerations

- Input validation for uploaded files
- Rate limiting for API endpoints
- Secure file handling
- Environment variable protection

## 📈 Scaling

- **Horizontal**: Deploy multiple instances behind load balancer
- **Vertical**: Increase CPU/memory resources
- **GPU**: Add GPU support for faster inference

## 🛠️ Troubleshooting

### Common Issues
1. **OpenCV GUI not working**: Install GUI-enabled OpenCV
2. **Tesseract not found**: Set TESSDATA_PREFIX correctly
3. **Model files missing**: Ensure all model files are present
4. **Memory issues**: Increase available RAM or reduce image size

### Performance Optimization
- Use GPU acceleration if available
- Optimize image preprocessing
- Implement frame skipping for real-time processing
- Use model quantization for faster inference

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review application logs
3. Verify all dependencies are installed
4. Ensure model files are present and accessible
