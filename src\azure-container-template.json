{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"containerGroupName": {"type": "string", "defaultValue": "license-plate-detector"}}, "resources": [{"type": "Microsoft.ContainerInstance/containerGroups", "apiVersion": "2019-12-01", "name": "[parameters('containerGroupName')]", "location": "[resourceGroup().location]", "properties": {"containers": [{"name": "license-plate-detector", "properties": {"image": "your-registry/license-plate-detector:latest", "ports": [{"port": 5000, "protocol": "TCP"}], "resources": {"requests": {"cpu": 2, "memoryInGB": 4}}}}], "osType": "Linux", "ipAddress": {"type": "Public", "ports": [{"port": 5000, "protocol": "TCP"}]}}}]}