# This code is written at BigVision LLC. It is based on the OpenCV project. It is subject to the license terms in the LICENSE file found in this distribution and at http://opencv.org/license.html

import cv2 as cv
import argparse
import sys
import numpy as np
import os.path
import pytesseract
import re
import csv
import datetime
import os

# Configure Tesseract path
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Initialize the parameters
confThreshold = 0.5  # Confidence threshold
nmsThreshold = 0.4  # Non-maximum suppression threshold

inpWidth = 416  # 608     # Width of network's input image
inpHeight = 416  # 608     # Height of network's input image

parser = argparse.ArgumentParser(
    description='Object Detection using YOLO in OPENCV')
parser.add_argument('--image', help='Path to image file.')
parser.add_argument('--image-dir', help='Path to directory containing images.')
parser.add_argument('--video', help='Path to video file.')
args = parser.parse_args()

# Load names of classes
classesFile = "../model/classes.names"

classes = None
with open(classesFile, 'rt') as f:
    classes = f.read().rstrip('\n').split('\n')

# Give the configuration and weight files for the model and load the network using them.

modelConfiguration = "../model/config/darknet-yolov3.cfg"
modelWeights = "../model/weights/model.weights"

net = cv.dnn.readNetFromDarknet(modelConfiguration, modelWeights)
net.setPreferableBackend(cv.dnn.DNN_BACKEND_OPENCV)
net.setPreferableTarget(cv.dnn.DNN_TARGET_CPU)

# Global variables for license plate detection
detected_plates = []  # Store detected license plate texts
csv_filename = "license_plate_detections.csv"
detection_counter = 0

# Get the names of the output layers


def initialize_csv():
    """Initialize CSV file with headers if it doesn't exist and get next detection ID"""
    global detection_counter

    if not os.path.exists(csv_filename):
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['detection_id', 'timestamp', 'source_type', 'source_name',
                         'confidence', 'bounding_box', 'plate_text', 'plate_image_file', 'notes']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            print(f"📊 Created CSV file: {csv_filename}")
        detection_counter = 0
    else:
        # Read existing CSV to get the highest detection_id
        try:
            with open(csv_filename, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                max_id = 0
                for row in reader:
                    try:
                        current_id = int(row['detection_id'])
                        max_id = max(max_id, current_id)
                    except (ValueError, KeyError):
                        continue
                detection_counter = max_id
                print(f"📊 Using existing CSV file: {csv_filename} (next ID: {detection_counter + 1})")
        except Exception as e:
            print(f"⚠️ Error reading existing CSV: {e}")
            detection_counter = 0

def save_detection_to_csv(detection_id, source_type, source_name, confidence,
                         bounding_box, plate_text, plate_image_file, notes=""):
    """Save detection details to CSV file"""
    try:
        with open(csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['detection_id', 'timestamp', 'source_type', 'source_name',
                         'confidence', 'bounding_box', 'plate_text', 'plate_image_file', 'notes']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writerow({
                'detection_id': detection_id,
                'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'source_type': source_type,
                'source_name': source_name,
                'confidence': f"{confidence:.4f}",
                'bounding_box': bounding_box,
                'plate_text': plate_text,
                'plate_image_file': plate_image_file,
                'notes': notes
            })
            print(f"💾 Saved detection to CSV: ID {detection_id}")
    except Exception as e:
        print(f"❌ Error saving to CSV: {e}")

def getOutputsNames(net):
    # Get the names of all the layers in the network
    layersNames = net.getLayerNames()
    # Get the names of the output layers, i.e. the layers with unconnected outputs
    # Handle both old and new OpenCV versions
    unconnected = net.getUnconnectedOutLayers()
    if len(unconnected.shape) == 1:
        # New OpenCV version returns 1D array
        return [layersNames[i - 1] for i in unconnected]
    else:
        # Old OpenCV version returns 2D array
        return [layersNames[i[0] - 1] for i in unconnected]

def extract_license_plate_text(frame, x, y, w, h, confidence, source_type, source_name):
    """Extract and save license plate region, perform OCR, save to CSV"""
    global detected_plates, detection_counter

    try:
        detection_counter += 1

        # Extract the license plate region
        plate_roi = frame[y:y+h, x:x+w]

        # Save the license plate region for manual inspection
        plate_filename = f"detected_plate_{detection_counter}.jpg"
        cv.imwrite(plate_filename, plate_roi)

        # Perform OCR on the license plate
        plate_text = perform_ocr_on_plate(plate_roi)

        # If OCR fails, use placeholder
        if not plate_text:
            plate_text = f"PLATE_{detection_counter}"
            notes = "OCR failed - using placeholder"
        else:
            notes = "OCR successful"

        # Create bounding box string
        bounding_box = f"({x},{y},{w},{h})"

        # Save to CSV
        save_detection_to_csv(
            detection_id=detection_counter,
            source_type=source_type,
            source_name=source_name,
            confidence=confidence,
            bounding_box=bounding_box,
            plate_text=plate_text,
            plate_image_file=plate_filename,
            notes=notes
        )

        if plate_text not in detected_plates:
            detected_plates.append(plate_text)
            print(f"🚗 License Plate Detected and Saved: {plate_filename}")
            print(f"   Extracted text: {plate_text}")
            print(f"   Confidence: {confidence:.4f}")
            print(f"   Bounding box: {bounding_box}")

        return plate_text

    except Exception as e:
        print(f"Plate extraction error: {e}")
        return None

def perform_ocr_on_plate(plate_roi):
    """Perform OCR on license plate region"""
    try:
        # Resize for better OCR accuracy
        scale_factor = 3
        plate_roi = cv.resize(plate_roi, None, fx=scale_factor, fy=scale_factor, interpolation=cv.INTER_CUBIC)

        # Convert to grayscale
        gray = cv.cvtColor(plate_roi, cv.COLOR_BGR2GRAY)

        # Apply image preprocessing for better OCR
        # Apply Gaussian blur to reduce noise
        blurred = cv.GaussianBlur(gray, (5, 5), 0)

        # Apply threshold to get binary image
        _, thresh = cv.threshold(blurred, 0, 255, cv.THRESH_BINARY + cv.THRESH_OTSU)

        # Apply morphological operations to clean up the image
        kernel = cv.getStructuringElement(cv.MORPH_RECT, (3, 3))
        cleaned = cv.morphologyEx(thresh, cv.MORPH_CLOSE, kernel)

        # Save preprocessed image for debugging
        debug_filename = f"debug_preprocessed_{detection_counter}.jpg"
        cv.imwrite(debug_filename, cleaned)

        # Use Tesseract OCR to extract text
        # Configure Tesseract for license plate recognition
        custom_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        text = pytesseract.image_to_string(cleaned, config=custom_config)

        # Clean up the extracted text
        text = text.strip().replace(' ', '').replace('\n', '').replace('\r', '')

        # Filter out very short or invalid results
        if len(text) >= 3 and re.match(r'^[A-Z0-9]+$', text):
            print(f"   ✅ OCR Success: '{text}'")
            return text
        else:
            print(f"   ❌ OCR Failed: Invalid text '{text}' (length: {len(text)})")
            return None

    except Exception as e:
        print(f"   ❌ OCR Error: {e}")
        return None

# Draw the predicted bounding box


def drawPred(classId, conf, left, top, right, bottom, source_type="unknown", source_name="unknown"):
    # Draw a bounding box.
    cv.rectangle(frame, (left, top), (right, bottom), (0, 255, 0), 7)

    # Extract license plate text using OCR
    width = right - left
    height = bottom - top
    plate_text = extract_license_plate_text(frame, left, top, width, height, conf, source_type, source_name)

    label = '%.2f' % conf

    # Get the label for the class name and its confidence
    if classes:
        assert(classId < len(classes))
        label = '%s: %s' % (classes[classId], label)

    # Add extracted text to label if available
    if plate_text:
        label += f' | {plate_text}'

    # Display the label at the top of the bounding box
    labelSize, baseLine = cv.getTextSize(
        label, cv.FONT_HERSHEY_SIMPLEX, 1, 1)
    top_label = max(top, labelSize[1])
    cv.rectangle(frame, (left, top_label - round(1.7*labelSize[1])), (left + round(
        1.3*labelSize[0]), top_label + baseLine), (255, 0, 255), cv.FILLED)
    cv.putText(frame, label, (left, top_label),
               cv.FONT_HERSHEY_SIMPLEX, 1.3, (255, 255, 255), 2)

    # If we have extracted text, also display it prominently below the box
    if plate_text:
        # Display extracted text in a larger, more visible way
        font_scale = 1.2
        thickness = 3
        text_size = cv.getTextSize(plate_text, cv.FONT_HERSHEY_SIMPLEX, font_scale, thickness)[0]
        text_x = max(0, left + (width - text_size[0]) // 2)
        text_y = min(frame.shape[0] - 20, bottom + 50)

        # Background for the extracted text (bright yellow)
        padding = 15
        cv.rectangle(frame, (text_x - padding, text_y - text_size[1] - padding),
                    (text_x + text_size[0] + padding, text_y + padding), (0, 255, 255), cv.FILLED)

        # Border for the text background
        cv.rectangle(frame, (text_x - padding, text_y - text_size[1] - padding),
                    (text_x + text_size[0] + padding, text_y + padding), (0, 0, 0), 2)

        # The extracted text (black on yellow background)
        cv.putText(frame, plate_text, (text_x, text_y),
                  cv.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), thickness)

# Remove the bounding boxes with low confidence using non-maxima suppression


def postprocess(frame, outs, source_type="unknown", source_name="unknown"):
    frameHeight = frame.shape[0]
    frameWidth = frame.shape[1]

    classIds = []
    confidences = []
    boxes = []
    # Scan through all the bounding boxes output from the network and keep only the
    # ones with high confidence scores. Assign the box's class label as the class with the highest score.
    classIds = []
    confidences = []
    boxes = []
    for out in outs:
        print("out.shape : ", out.shape)
        for detection in out:
            # if detection[4]>0.001:
            scores = detection[5:]
            classId = np.argmax(scores)
            # if scores[classId]>confThreshold:
            confidence = scores[classId]
            if detection[4] > confThreshold:
                print(detection[4], " - ", scores[classId],
                      " - th : ", confThreshold)
                print(detection)
            if confidence > confThreshold:
                center_x = int(detection[0] * frameWidth)
                center_y = int(detection[1] * frameHeight)
                width = int(detection[2] * frameWidth)
                height = int(detection[3] * frameHeight)
                left = int(center_x - width / 2)
                top = int(center_y - height / 2)
                classIds.append(classId)
                confidences.append(float(confidence))
                boxes.append([left, top, width, height])

    # Perform non maximum suppression to eliminate redundant overlapping boxes with
    # lower confidences.
    indices = cv.dnn.NMSBoxes(boxes, confidences, confThreshold, nmsThreshold)
    if len(indices) > 0:
        # Handle both old and new OpenCV versions
        if isinstance(indices[0], (list, np.ndarray)) and len(indices[0]) > 0:
            # Old OpenCV version returns 2D array
            indices = indices.flatten() # type: ignore

        for i in indices:
            box = boxes[i]
            left = box[0]
            top = box[1]
            width = box[2]
            height = box[3]
            drawPred(classIds[i], confidences[i], left,
                     top, left + width, top + height, source_type, source_name)


# Initialize CSV file for logging detections
initialize_csv()

# Process inputs - Try to create GUI window
winName = 'YOLO License Plate Detection - Real Time'
try:
    cv.namedWindow(winName, cv.WINDOW_NORMAL)
    cv.resizeWindow(winName, 1000, 700)
    gui_available = True
    print("🖥️ GUI Window Created: Press 'q' or ESC to quit")
except Exception as e:
    print(f"⚠️ GUI not available ({e}), using image saving mode")
    gui_available = False

# Determine source type and name for CSV logging
source_type = "unknown"
source_name = "unknown"

outputFile = "yolo_out_py.avi"
if (args.image):
    # Open the image file
    if not os.path.isfile(args.image):
        print("Input image file ", args.image, " doesn't exist")
        sys.exit(1)
    cap = cv.VideoCapture(args.image)
    outputFile = args.image[:-4]+'_yolo_out_py.jpg'
    source_type = "image"
    source_name = os.path.basename(args.image)
elif (args.image_dir):
    if not os.path.isdir(args.image_dir):
        print("Input image dir ", args.image_dir, " doesn't exist")
        sys.exit(1)
    for image_path in [k for k in os.listdir(args.image_dir) if 'out_py' not in k]:
        os.system('python object_detection_yolo.py --image={}'.format(os.path.join(args.image_dir, image_path)))
    sys.exit(1)
elif (args.video):
    # Open the video file
    if not os.path.isfile(args.video):
        print("Input video file ", args.video, " doesn't exist")
        sys.exit(1)
    cap = cv.VideoCapture(args.video)
    outputFile = args.video[:-4]+'_yolo_out_py.avi'
    source_type = "video"
    source_name = os.path.basename(args.video)
else:
    # Webcam input
    cap = cv.VideoCapture(0)
    # Set webcam properties for better performance
    cap.set(cv.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv.CAP_PROP_FPS, 30)
    source_type = "webcam"
    source_name = "webcam_0"

# Get the video writer initialized to save the output video
if not (args.image):
    vid_writer = cv.VideoWriter(outputFile, cv.VideoWriter_fourcc('M', 'J', 'P', 'G'), 30, (round( # type: ignore
        cap.get(cv.CAP_PROP_FRAME_WIDTH)), round(cap.get(cv.CAP_PROP_FRAME_HEIGHT))))

frame_count = 0
print("🎥 Starting detection... Press 'q' or ESC to quit")

while True:
    # Check for quit key first (only if GUI is available)
    if gui_available:
        key = cv.waitKey(1) & 0xFF
        if key == ord('q') or key == 27:  # 'q' or ESC key
            print("👋 Exiting...")
            break

    # get frame from the video
    hasFrame, frame = cap.read()

    # Stop the program if reached end of video
    if not hasFrame:
        print("✅ Done processing !!!")
        if not (args.image):
            print(f"📁 Output file is stored as: {outputFile}")
        # For images, wait for key press before closing
        if args.image:
            print("Press any key to close...")
            cv.waitKey(0)
        break

    frame_count += 1

    # Show progress for webcam
    if not (args.image or args.video):
        if frame_count % 30 == 0:
            print(f"📊 Processed {frame_count} frames... (Press 'q' to quit)")

    # Create a 4D blob from a frame.
    blob = cv.dnn.blobFromImage(
        frame, 1/255, (inpWidth, inpHeight), [0, 0, 0], 1, crop=False) # type: ignore

    # Sets the input to the network
    net.setInput(blob)

    # Runs the forward pass to get output of the output layers
    outs = net.forward(getOutputsNames(net)) # type: ignore

    # Remove the bounding boxes with low confidence
    postprocess(frame, outs, source_type, source_name)

    # Put efficiency information and UI elements on frame
    t, _ = net.getPerfProfile()
    inference_time = t * 1000.0 / cv.getTickFrequency()

    # Add information overlay to frame
    overlay_y = 30
    cv.putText(frame, f"Inference: {inference_time:.1f}ms", (10, overlay_y), cv.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    overlay_y += 30
    cv.putText(frame, f"Frame: {frame_count}", (10, overlay_y), cv.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    overlay_y += 30
    cv.putText(frame, f"Detections: {len(detected_plates)}", (10, overlay_y), cv.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    # Add source info
    overlay_y += 30
    cv.putText(frame, f"Source: {source_type} - {source_name}", (10, overlay_y), cv.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

    # Add controls info at bottom
    cv.putText(frame, "Press 'q' or ESC to quit", (10, frame.shape[0] - 20), cv.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

    # Display in GUI window if available
    if gui_available:
        cv.imshow(winName, frame)

    # Save output files
    if (args.image):
        cv.imwrite(outputFile, frame.astype(np.uint8))
        print(f"💾 Saved processed image: {outputFile}")
    elif (args.video):
        vid_writer.write(frame.astype(np.uint8)) # type: ignore

# Clean up
cap.release()
if gui_available:
    cv.destroyAllWindows()
print("🎯 Detection completed!")
print(f"📊 Total detections: {len(detected_plates)}")
print(f"📁 Results saved to: {csv_filename}")
if detected_plates:
    print("🚗 Detected license plates:")
    for i, plate in enumerate(detected_plates, 1):
        print(f"   {i}. {plate}")
