#!/bin/bash
# Azure Deployment Script

RESOURCE_GROUP="license-plate-detector-rg"
CONTAINER_GROUP="license-plate-detector"
LOCATION="eastus"

echo "🚀 Deploying to Azure..."

# Create resource group
az group create --name $RESOURCE_GROUP --location $LOCATION

# Deploy container
az deployment group create \
  --resource-group $RESOURCE_GROUP \
  --template-file azure-container-template.json \
  --parameters containerGroupName=$CONTAINER_GROUP

echo "✅ Deployment completed!"
echo "🔗 Your application URL:"
az container show --resource-group $RESOURCE_GROUP --name $CONTAINER_GROUP --query ipAddress.fqdn
