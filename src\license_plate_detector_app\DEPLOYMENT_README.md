# YOLO License Plate Detection System - Desktop App

## Installation
1. Run `install.bat` to install dependencies
2. Double-click `run_detector.bat` to start the application

## Features
- Real-time webcam license plate detection
- Image and video processing
- Automatic OCR text extraction
- CSV logging of all detections
- OpenCV GUI display

## Usage
- Choose option 1 for real-time webcam detection
- Choose option 2 to process a single image
- Choose option 3 to process a video file

## Output
- Detected license plates saved as individual images
- All detections logged to `license_plate_detections.csv`
- Processed images/videos saved with detection boxes

## Controls
- Press 'q' or ESC to quit the GUI
- All results are automatically saved
