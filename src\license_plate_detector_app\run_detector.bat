@echo off
echo Starting YOLO License Plate Detection System...
echo.
echo Choose an option:
echo 1. Real-time webcam detection
echo 2. Process single image
echo 3. Process video file
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    python license_plate_detector.py
) else if "%choice%"=="2" (
    set /p image_path="Enter image path: "
    python license_plate_detector.py --image "%image_path%"
) else if "%choice%"=="3" (
    set /p video_path="Enter video path: "
    python license_plate_detector.py --video "%video_path%"
) else (
    echo Invalid choice!
    pause
)

echo.
echo Detection completed! Check the CSV file for results.
pause
