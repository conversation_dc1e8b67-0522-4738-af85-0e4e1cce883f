#!/usr/bin/env python3
"""
Web Application for YOLO License Plate Detection
Deploy as a web service using Flask
"""

from flask import Flask, render_template, request, jsonify, send_file, Response
import cv2 as cv
import numpy as np
import os
import base64
import io
from PIL import Image
import json
import datetime
import csv
from werkzeug.utils import secure_filename
import threading
import time

# Import your detection functions
import sys
sys.path.append('src')

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Global variables for webcam streaming
camera = None
detection_active = False

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and process image"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Process the image
        result = process_image(filepath)
        return jsonify(result)
    
    return jsonify({'error': 'Invalid file type'}), 400

@app.route('/webcam_stream')
def webcam_stream():
    """Stream webcam with detection"""
    return Response(generate_webcam_frames(),
                   mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/start_webcam', methods=['POST'])
def start_webcam():
    """Start webcam detection"""
    global detection_active
    detection_active = True
    return jsonify({'status': 'started'})

@app.route('/stop_webcam', methods=['POST'])
def stop_webcam():
    """Stop webcam detection"""
    global detection_active
    detection_active = False
    return jsonify({'status': 'stopped'})

@app.route('/detections')
def get_detections():
    """Get all detections from CSV"""
    detections = []
    csv_file = 'src/license_plate_detections.csv'
    
    if os.path.exists(csv_file):
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            detections = list(reader)
    
    return jsonify(detections)

@app.route('/download_csv')
def download_csv():
    """Download detections CSV"""
    csv_file = 'src/license_plate_detections.csv'
    if os.path.exists(csv_file):
        return send_file(csv_file, as_attachment=True, 
                        download_name='license_plate_detections.csv')
    return jsonify({'error': 'No detections file found'}), 404

def allowed_file(filename):
    """Check if file extension is allowed"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def process_image(image_path):
    """Process uploaded image and return results"""
    try:
        # Here you would integrate your YOLO detection code
        # For now, return a mock result
        result = {
            'success': True,
            'detections': [
                {
                    'plate_text': 'ABC123',
                    'confidence': 0.95,
                    'bounding_box': [100, 100, 200, 50]
                }
            ],
            'processed_image': encode_image_to_base64(image_path)
        }
        return result
    except Exception as e:
        return {'success': False, 'error': str(e)}

def encode_image_to_base64(image_path):
    """Convert image to base64 for web display"""
    with open(image_path, "rb") as img_file:
        return base64.b64encode(img_file.read()).decode('utf-8')

def generate_webcam_frames():
    """Generate webcam frames with detection"""
    global camera, detection_active
    
    camera = cv.VideoCapture(0)
    
    while True:
        if not detection_active:
            time.sleep(0.1)
            continue
            
        success, frame = camera.read()
        if not success:
            break
        
        # Add your YOLO detection here
        # For now, just return the frame
        
        ret, buffer = cv.imencode('.jpg', frame)
        frame = buffer.tobytes()
        
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')

if __name__ == '__main__':
    # Create templates directory and HTML template
    os.makedirs('templates', exist_ok=True)
    
    html_template = '''<!DOCTYPE html>
<html>
<head>
    <title>YOLO License Plate Detection</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .webcam-container { text-align: center; }
        #webcam-feed { max-width: 100%; border: 2px solid #333; }
        .results { margin-top: 20px; }
        .detection-item { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 YOLO License Plate Detection System</h1>
            <p>Upload images or use webcam for real-time license plate detection</p>
        </div>
        
        <div class="section">
            <h2>📁 Upload Image</h2>
            <div class="upload-area">
                <input type="file" id="file-input" accept="image/*">
                <button class="btn" onclick="uploadFile()">Process Image</button>
            </div>
            <div id="upload-results" class="results"></div>
        </div>
        
        <div class="section">
            <h2>📹 Webcam Detection</h2>
            <div class="webcam-container">
                <img id="webcam-feed" src="/webcam_stream" style="display:none;">
                <br><br>
                <button class="btn" onclick="startWebcam()">Start Webcam</button>
                <button class="btn" onclick="stopWebcam()">Stop Webcam</button>
            </div>
        </div>
        
        <div class="section">
            <h2>📊 Detection Results</h2>
            <button class="btn" onclick="loadDetections()">Refresh Results</button>
            <button class="btn" onclick="downloadCSV()">Download CSV</button>
            <div id="detections-list" class="results"></div>
        </div>
    </div>
    
    <script>
        function uploadFile() {
            const fileInput = document.getElementById('file-input');
            const file = fileInput.files[0];
            if (!file) {
                alert('Please select a file');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('upload-results').innerHTML = 
                    '<h3>Results:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            });
        }
        
        function startWebcam() {
            fetch('/start_webcam', { method: 'POST' })
            .then(() => {
                document.getElementById('webcam-feed').style.display = 'block';
            });
        }
        
        function stopWebcam() {
            fetch('/stop_webcam', { method: 'POST' })
            .then(() => {
                document.getElementById('webcam-feed').style.display = 'none';
            });
        }
        
        function loadDetections() {
            fetch('/detections')
            .then(response => response.json())
            .then(data => {
                let html = '<h3>Recent Detections:</h3>';
                data.forEach(detection => {
                    html += `<div class="detection-item">
                        <strong>Plate:</strong> ${detection.plate_text} | 
                        <strong>Confidence:</strong> ${detection.confidence} | 
                        <strong>Time:</strong> ${detection.timestamp}
                    </div>`;
                });
                document.getElementById('detections-list').innerHTML = html;
            });
        }
        
        function downloadCSV() {
            window.location.href = '/download_csv';
        }
        
        // Load detections on page load
        loadDetections();
    </script>
</body>
</html>'''
    
    with open('templates/index.html', 'w') as f:
        f.write(html_template)
    
    print("🌐 Starting web application...")
    print("🔗 Open your browser and go to: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
