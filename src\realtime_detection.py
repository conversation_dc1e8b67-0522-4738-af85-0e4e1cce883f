#!/usr/bin/env python3
"""
Real-time YOLO License Plate Detection with Live Display
This version creates a simple web interface to view the detection results in real-time
"""

import cv2 as cv
import numpy as np
import os
import time
import threading
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socketserver
import base64

# Initialize the parameters
confThreshold = 0.5  # Confidence threshold
nmsThreshold = 0.4  # Non-maximum suppression threshold
inpWidth = 416  # Width of network's input image
inpHeight = 416  # Height of network's input image

# Load names of classes
classesFile = "../model/classes.names"
classes = None
with open(classesFile, 'rt') as f:
    classes = f.read().rstrip('\n').split('\n')

# Give the configuration and weight files for the model and load the network using them.
modelConfiguration = "../model/config/darknet-yolov3.cfg"
modelWeights = "../model/weights/model.weights"

net = cv.dnn.readNetFromDarknet(modelConfiguration, modelWeights)
net.setPreferableBackend(cv.dnn.DNN_BACKEND_OPENCV)
net.setPreferableTarget(cv.dnn.DNN_TARGET_CPU)

# Global variables for web display
latest_frame = None
detection_active = True

def getOutputsNames(net):
    """Get the names of the output layers"""
    layersNames = net.getLayerNames()
    unconnected = net.getUnconnectedOutLayers()
    if len(unconnected.shape) == 1:
        return [layersNames[i - 1] for i in unconnected]
    else:
        return [layersNames[i[0] - 1] for i in unconnected]

def drawPred(frame, classId, conf, left, top, right, bottom):
    """Draw the predicted bounding box"""
    cv.rectangle(frame, (left, top), (right, bottom), (0, 255, 0), 3)
    
    label = '%.2f' % conf
    if classes:
        assert(classId < len(classes))
        label = '%s: %s' % (classes[classId], label)
    
    labelSize, baseLine = cv.getTextSize(label, cv.FONT_HERSHEY_SIMPLEX, 0.5, 1)
    top = max(top, labelSize[1])
    cv.rectangle(frame, (left, top - round(1.5*labelSize[1])), 
                (left + round(1.2*labelSize[0]), top + baseLine), (0, 255, 0), cv.FILLED)
    cv.putText(frame, label, (left, top), cv.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

def postprocess(frame, outs):
    """Remove the bounding boxes with low confidence using non-maxima suppression"""
    frameHeight = frame.shape[0]
    frameWidth = frame.shape[1]
    
    classIds = []
    confidences = []
    boxes = []
    
    for out in outs:
        for detection in out:
            scores = detection[5:]
            classId = np.argmax(scores)
            confidence = scores[classId]
            if confidence > confThreshold:
                center_x = int(detection[0] * frameWidth)
                center_y = int(detection[1] * frameHeight)
                width = int(detection[2] * frameWidth)
                height = int(detection[3] * frameHeight)
                left = int(center_x - width / 2)
                top = int(center_y - height / 2)
                classIds.append(classId)
                confidences.append(float(confidence))
                boxes.append([left, top, width, height])
    
    # Perform non maximum suppression
    indices = cv.dnn.NMSBoxes(boxes, confidences, confThreshold, nmsThreshold)
    if len(indices) > 0:
        if isinstance(indices[0], (list, np.ndarray)) and len(indices[0]) > 0:
            indices = indices.flatten()
        
        for i in indices:
            box = boxes[i]
            left = box[0]
            top = box[1]
            width = box[2]
            height = box[3]
            drawPred(frame, classIds[i], confidences[i], left, top, left + width, top + height)

def detection_loop():
    """Main detection loop"""
    global latest_frame, detection_active
    
    # Initialize webcam
    cap = cv.VideoCapture(0)
    cap.set(cv.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv.CAP_PROP_FPS, 30)
    
    frame_count = 0
    
    print("Starting real-time detection...")
    print("Open your web browser and go to: http://localhost:8000")
    print("Press Ctrl+C to stop")
    
    try:
        while detection_active:
            hasFrame, frame = cap.read()
            if not hasFrame:
                break
            
            frame_count += 1
            
            # Create a 4D blob from a frame
            blob = cv.dnn.blobFromImage(frame, 1/255, (inpWidth, inpHeight), [0, 0, 0], 1, crop=False)
            
            # Sets the input to the network
            net.setInput(blob)
            
            # Runs the forward pass to get output of the output layers
            outs = net.forward(getOutputsNames(net))
            
            # Remove the bounding boxes with low confidence
            postprocess(frame, outs)
            
            # Put efficiency information
            t, _ = net.getPerfProfile()
            label = 'Inference time: %.2f ms' % (t * 1000.0 / cv.getTickFrequency())
            cv.putText(frame, label, (10, 30), cv.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv.putText(frame, f"Frame: {frame_count}", (10, 60), cv.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # Update the latest frame for web display
            latest_frame = frame.copy()
            
            # Small delay to prevent overwhelming the system
            time.sleep(0.03)  # ~30 FPS
            
    except KeyboardInterrupt:
        print("\nStopping detection...")
    finally:
        cap.release()
        detection_active = False

class VideoStreamHandler(SimpleHTTPRequestHandler):
    """HTTP handler for video streaming"""
    
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>YOLO License Plate Detection - Real Time</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; background-color: #f0f0f0; }
                    h1 { color: #333; }
                    #video { border: 2px solid #333; margin: 20px; }
                    .info { margin: 10px; color: #666; }
                </style>
            </head>
            <body>
                <h1>🚗 YOLO License Plate Detection - Real Time</h1>
                <div class="info">Live webcam feed with license plate detection</div>
                <img id="video" src="/video_feed" width="640" height="480">
                <div class="info">Press Ctrl+C in the terminal to stop</div>
                <script>
                    // Auto-refresh the image
                    setInterval(function() {
                        document.getElementById('video').src = '/video_feed?' + new Date().getTime();
                    }, 100); // Update every 100ms
                </script>
            </body>
            </html>
            """
            self.wfile.write(html_content.encode())
            
        elif self.path.startswith('/video_feed'):
            if latest_frame is not None:
                # Encode frame as JPEG
                _, buffer = cv.imencode('.jpg', latest_frame)
                
                self.send_response(200)
                self.send_header('Content-type', 'image/jpeg')
                self.send_header('Cache-Control', 'no-cache')
                self.end_headers()
                self.wfile.write(buffer.tobytes())
            else:
                self.send_response(404)
                self.end_headers()
        else:
            super().do_GET()

def start_web_server():
    """Start the web server"""
    PORT = 8000
    Handler = VideoStreamHandler
    
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"Web server started at http://localhost:{PORT}")
        httpd.serve_forever()

if __name__ == "__main__":
    # Start detection in a separate thread
    detection_thread = threading.Thread(target=detection_loop)
    detection_thread.daemon = True
    detection_thread.start()
    
    # Start web server (this will block)
    try:
        start_web_server()
    except KeyboardInterrupt:
        print("\nShutting down...")
        detection_active = False
