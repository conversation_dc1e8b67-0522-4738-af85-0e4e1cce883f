#!/bin/bash
# Google Cloud Platform Deployment Script

PROJECT_ID="your-project-id"
SERVICE_NAME="license-plate-detector"

echo "🚀 Deploying to Google Cloud..."

# Build and push Docker image
docker build -t gcr.io/$PROJECT_ID/$SERVICE_NAME .
docker push gcr.io/$PROJECT_ID/$SERVICE_NAME

# Deploy to Cloud Run
gcloud run deploy $SERVICE_NAME \
  --image gcr.io/$PROJECT_ID/$SERVICE_NAME \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --memory 4Gi \
  --cpu 2

echo "✅ Deployment completed!"
echo "🔗 Your application URL:"
gcloud run services describe $SERVICE_NAME --region us-central1 --format="value(status.url)"
