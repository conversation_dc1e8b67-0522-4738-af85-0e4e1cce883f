#!/usr/bin/env python3
"""
Cloud Deployment Script for YOLO License Plate Detection
Supports AWS, Google Cloud, and Azure deployments
"""

import os
import json
import subprocess
import sys

def create_aws_deployment():
    """Create AWS deployment files"""
    
    # Create Elastic Beanstalk configuration
    eb_config = {
        "AWSEBDockerrunVersion": 2,
        "containerDefinitions": [
            {
                "name": "license-plate-detector",
                "image": "your-docker-registry/license-plate-detector:latest",
                "essential": True,
                "memory": 2048,
                "portMappings": [
                    {
                        "hostPort": 80,
                        "containerPort": 5000,
                        "protocol": "tcp"
                    }
                ],
                "environment": [
                    {
                        "name": "PYTHONPATH",
                        "value": "/app"
                    }
                ]
            }
        ]
    }
    
    with open('Dockerrun.aws.json', 'w') as f:
        json.dump(eb_config, f, indent=2)
    
    # Create deployment script
    aws_deploy_script = '''#!/bin/bash
# AWS Deployment Script

echo "🚀 Deploying to AWS Elastic Beanstalk..."

# Initialize EB application (run once)
# eb init license-plate-detector --platform docker --region us-west-2

# Create environment (run once)
# eb create production --instance-type t3.medium

# Deploy application
eb deploy

echo "✅ Deployment completed!"
echo "🔗 Your application URL:"
eb status | grep "CNAME"
'''
    
    with open('deploy_aws.sh', 'w', encoding='utf-8') as f:
        f.write(aws_deploy_script)
    
    os.chmod('deploy_aws.sh', 0o755)
    
    print("✅ AWS deployment files created:")
    print("   - Dockerrun.aws.json")
    print("   - deploy_aws.sh")

def create_gcp_deployment():
    """Create Google Cloud Platform deployment files"""
    
    # Create app.yaml for App Engine
    app_yaml = '''runtime: python310

env_variables:
  PYTHONPATH: /app
  TESSDATA_PREFIX: /usr/share/tesseract-ocr/4.00/tessdata

automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6

resources:
  cpu: 2
  memory_gb: 4
  disk_size_gb: 10
'''
    
    with open('app.yaml', 'w') as f:
        f.write(app_yaml)
    
    # Create Cloud Run deployment
    cloudrun_yaml = '''apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: license-plate-detector
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
    spec:
      containerConcurrency: 10
      containers:
      - image: gcr.io/PROJECT_ID/license-plate-detector
        ports:
        - containerPort: 5000
        env:
        - name: PYTHONPATH
          value: "/app"
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
'''
    
    with open('cloudrun-service.yaml', 'w') as f:
        f.write(cloudrun_yaml)
    
    # Create deployment script
    gcp_deploy_script = '''#!/bin/bash
# Google Cloud Platform Deployment Script

PROJECT_ID="your-project-id"
SERVICE_NAME="license-plate-detector"

echo "🚀 Deploying to Google Cloud..."

# Build and push Docker image
docker build -t gcr.io/$PROJECT_ID/$SERVICE_NAME .
docker push gcr.io/$PROJECT_ID/$SERVICE_NAME

# Deploy to Cloud Run
gcloud run deploy $SERVICE_NAME \\
  --image gcr.io/$PROJECT_ID/$SERVICE_NAME \\
  --platform managed \\
  --region us-central1 \\
  --allow-unauthenticated \\
  --memory 4Gi \\
  --cpu 2

echo "✅ Deployment completed!"
echo "🔗 Your application URL:"
gcloud run services describe $SERVICE_NAME --region us-central1 --format="value(status.url)"
'''
    
    with open('deploy_gcp.sh', 'w', encoding='utf-8') as f:
        f.write(gcp_deploy_script)
    
    os.chmod('deploy_gcp.sh', 0o755)
    
    print("✅ GCP deployment files created:")
    print("   - app.yaml")
    print("   - cloudrun-service.yaml")
    print("   - deploy_gcp.sh")

def create_azure_deployment():
    """Create Azure deployment files"""
    
    # Create Azure Container Instances deployment
    aci_template = {
        "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
        "contentVersion": "*******",
        "parameters": {
            "containerGroupName": {
                "type": "string",
                "defaultValue": "license-plate-detector"
            }
        },
        "resources": [
            {
                "type": "Microsoft.ContainerInstance/containerGroups",
                "apiVersion": "2019-12-01",
                "name": "[parameters('containerGroupName')]",
                "location": "[resourceGroup().location]",
                "properties": {
                    "containers": [
                        {
                            "name": "license-plate-detector",
                            "properties": {
                                "image": "your-registry/license-plate-detector:latest",
                                "ports": [
                                    {
                                        "port": 5000,
                                        "protocol": "TCP"
                                    }
                                ],
                                "resources": {
                                    "requests": {
                                        "cpu": 2,
                                        "memoryInGB": 4
                                    }
                                }
                            }
                        }
                    ],
                    "osType": "Linux",
                    "ipAddress": {
                        "type": "Public",
                        "ports": [
                            {
                                "port": 5000,
                                "protocol": "TCP"
                            }
                        ]
                    }
                }
            }
        ]
    }
    
    with open('azure-container-template.json', 'w') as f:
        json.dump(aci_template, f, indent=2)
    
    # Create deployment script
    azure_deploy_script = '''#!/bin/bash
# Azure Deployment Script

RESOURCE_GROUP="license-plate-detector-rg"
CONTAINER_GROUP="license-plate-detector"
LOCATION="eastus"

echo "🚀 Deploying to Azure..."

# Create resource group
az group create --name $RESOURCE_GROUP --location $LOCATION

# Deploy container
az deployment group create \\
  --resource-group $RESOURCE_GROUP \\
  --template-file azure-container-template.json \\
  --parameters containerGroupName=$CONTAINER_GROUP

echo "✅ Deployment completed!"
echo "🔗 Your application URL:"
az container show --resource-group $RESOURCE_GROUP --name $CONTAINER_GROUP --query ipAddress.fqdn
'''
    
    with open('deploy_azure.sh', 'w', encoding='utf-8') as f:
        f.write(azure_deploy_script)
    
    os.chmod('deploy_azure.sh', 0o755)
    
    print("✅ Azure deployment files created:")
    print("   - azure-container-template.json")
    print("   - deploy_azure.sh")

def create_deployment_guide():
    """Create comprehensive deployment guide"""
    
    guide_content = '''# 🚀 YOLO License Plate Detection - Deployment Guide

## Deployment Options

### 1. 🖥️ Desktop Application
```bash
python deploy_desktop.py
```
- Creates standalone desktop application
- Includes batch files for easy launching
- No internet connection required after installation

### 2. 🌐 Web Application (Local)
```bash
pip install flask
python web_app.py
```
- Access at http://localhost:5000
- Web interface for image upload and webcam detection
- Real-time results display

### 3. 🐳 Docker Deployment
```bash
# Build and run locally
docker build -t license-plate-detector .
docker run -p 5000:5000 license-plate-detector

# Or use Docker Compose
docker-compose up
```

### 4. ☁️ Cloud Deployment

#### AWS Elastic Beanstalk
```bash
# Install EB CLI
pip install awsebcli

# Deploy
./deploy_aws.sh
```

#### Google Cloud Platform
```bash
# Install gcloud CLI
# Set project ID in deploy_gcp.sh
./deploy_gcp.sh
```

#### Microsoft Azure
```bash
# Install Azure CLI
# Update registry in azure-container-template.json
./deploy_azure.sh
```

## 📋 Prerequisites

### System Requirements
- Python 3.8+
- OpenCV with GUI support
- Tesseract OCR
- 4GB+ RAM recommended
- GPU optional (for faster processing)

### Dependencies
```bash
pip install -r requirements.txt
```

## 🔧 Configuration

### Environment Variables
- `TESSDATA_PREFIX`: Path to Tesseract data files
- `PYTHONPATH`: Application path

### Model Files
Ensure these files are present:
- `model/weights/model.weights`
- `model/config/darknet-yolov3.cfg`
- `model/classes.names`

## 🚀 Quick Start

1. **Desktop**: Run `python deploy_desktop.py`
2. **Web**: Run `python web_app.py`
3. **Docker**: Run `docker-compose up`
4. **Cloud**: Choose your platform and run the deployment script

## 📊 Monitoring & Logs

- Detection results: `license_plate_detections.csv`
- Application logs: Check console output
- Error handling: Built-in exception handling

## 🔒 Security Considerations

- Input validation for uploaded files
- Rate limiting for API endpoints
- Secure file handling
- Environment variable protection

## 📈 Scaling

- **Horizontal**: Deploy multiple instances behind load balancer
- **Vertical**: Increase CPU/memory resources
- **GPU**: Add GPU support for faster inference

## 🛠️ Troubleshooting

### Common Issues
1. **OpenCV GUI not working**: Install GUI-enabled OpenCV
2. **Tesseract not found**: Set TESSDATA_PREFIX correctly
3. **Model files missing**: Ensure all model files are present
4. **Memory issues**: Increase available RAM or reduce image size

### Performance Optimization
- Use GPU acceleration if available
- Optimize image preprocessing
- Implement frame skipping for real-time processing
- Use model quantization for faster inference

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review application logs
3. Verify all dependencies are installed
4. Ensure model files are present and accessible
'''
    
    with open('DEPLOYMENT_GUIDE.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ Deployment guide created: DEPLOYMENT_GUIDE.md")

if __name__ == "__main__":
    print("🚀 Creating deployment files for YOLO License Plate Detection...")
    
    create_aws_deployment()
    create_gcp_deployment()
    create_azure_deployment()
    create_deployment_guide()
    
    print("\n🎉 All deployment files created successfully!")
    print("📖 Read DEPLOYMENT_GUIDE.md for detailed instructions")
    print("🔧 Choose your preferred deployment method and follow the guide")
