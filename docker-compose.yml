version: '3.8'

services:
  license-plate-detector:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
      - ./detections:/app/detections
    environment:
      - PYTHONPATH=/app
      - TESSDATA_PREFIX=/usr/share/tesseract-ocr/4.00/tessdata
    restart: unless-stopped
    
  # Optional: Add a database for storing detections
  # postgres:
  #   image: postgres:13
  #   environment:
  #     POSTGRES_DB: license_plates
  #     POSTGRES_USER: detector
  #     POSTGRES_PASSWORD: secure_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   restart: unless-stopped

# volumes:
#   postgres_data:
